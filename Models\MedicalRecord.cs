﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Midterm1212.Models
{
    public class MedicalRecord
    {
        [Key]
        public int record_id { get; set; }

        [Required]
        public int patient_id { get; set; }

        [Required]
        public int doctor_id { get; set; }

        [Required]
        public int appointment_id { get; set; }

        [StringLength(200)]
        public string diagnosis { get; set; }

        [StringLength(500)]
        public string prescription { get; set; }

        [StringLength(1000)]
        public string notes { get; set; }

        public DateTime record_date { get; set; }

        [ForeignKey("patient_id")]
        public Patient Patient { get; set; }

        [Foreign<PERSON><PERSON>("doctor_id")]
        public Doctor Doctor { get; set; }

        [Foreign<PERSON>ey("appointment_id")]
        public Appointment Appointment { get; set; }
    }
}
