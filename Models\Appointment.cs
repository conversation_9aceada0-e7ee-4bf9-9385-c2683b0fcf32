﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace Midterm1212.Models
{
    public class Appointment
    {
        [Key]
        public int appointment_id { get; set; }

        [Required]
        public int patient_id { get; set; }

        [Required]
        public int doctor_id { get; set; }

        public DateTime appointment_date { get; set; }

        public TimeSpan appointment_time { get; set; }

        [StringLength(50)]
        public string status { get; set; }

        [ForeignKey("patient_id")]
        public Patient Patient { get; set; }

        [ForeignKey("doctor_id")]
        public Doctor Doctor { get; set; }

        public ICollection<MedicalRecord> MedicalRecords { get; set; }
    }
}
