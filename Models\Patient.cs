﻿using System.ComponentModel.DataAnnotations;

namespace Midterm1212.Models
{
    public class Patient
    {
        [Key]
        public int patient_id { get; set; }

        [Required]
        [StringLength(100)]
        public string name { get; set; }

        public DateTime dob { get; set; }

        [StringLength(10)]
        public string gender { get; set; }

        [StringLength(15)]
        public string phone { get; set; }

        [StringLength(200)]
        public string address { get; set; }

        public ICollection<Appointment> Appointments { get; set; }
        public ICollection<MedicalRecord> MedicalRecords { get; set; }
    }
}
