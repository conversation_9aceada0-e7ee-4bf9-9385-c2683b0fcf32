﻿using System.ComponentModel.DataAnnotations;

namespace Midterm1212.Models
{
    public class Doctor
    {
        [Key]
        public int doctor_id { get; set; }

        [Required]
        [StringLength(100)]
        public string name { get; set; }

        [StringLength(50)]
        public string specialization { get; set; }

        [StringLength(15)]
        public string phone { get; set; }

        [StringLength(100)]
        public string email { get; set; }

        [StringLength(200)]
        public string image { get; set; }

        public int work_experience { get; set; }

        public ICollection<Appointment> Appointments { get; set; }
        public ICollection<MedicalRecord> MedicalRecords { get; set; }
    }
}
