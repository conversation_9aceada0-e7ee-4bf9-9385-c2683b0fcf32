// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Midterm1212.Models;

#nullable disable

namespace Midterm1212.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.16")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Midterm1212.Models.Appointment", b =>
                {
                    b.Property<int>("appointment_id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("appointment_id"));

                    b.Property<DateTime>("appointment_date")
                        .HasColumnType("datetime2");

                    b.Property<TimeSpan>("appointment_time")
                        .HasColumnType("time");

                    b.Property<int>("doctor_id")
                        .HasColumnType("int");

                    b.Property<int>("patient_id")
                        .HasColumnType("int");

                    b.Property<string>("status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("appointment_id");

                    b.HasIndex("doctor_id");

                    b.HasIndex("patient_id");

                    b.ToTable("Appointments", (string)null);
                });

            modelBuilder.Entity("Midterm1212.Models.Doctor", b =>
                {
                    b.Property<int>("doctor_id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("doctor_id"));

                    b.Property<string>("email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("image")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("phone")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.Property<string>("specialization")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("work_experience")
                        .HasColumnType("int");

                    b.HasKey("doctor_id");

                    b.ToTable("Doctors", (string)null);
                });

            modelBuilder.Entity("Midterm1212.Models.MedicalRecord", b =>
                {
                    b.Property<int>("record_id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("record_id"));

                    b.Property<int>("appointment_id")
                        .HasColumnType("int");

                    b.Property<string>("diagnosis")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("doctor_id")
                        .HasColumnType("int");

                    b.Property<string>("notes")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int>("patient_id")
                        .HasColumnType("int");

                    b.Property<string>("prescription")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime>("record_date")
                        .HasColumnType("datetime2");

                    b.HasKey("record_id");

                    b.HasIndex("appointment_id");

                    b.HasIndex("doctor_id");

                    b.HasIndex("patient_id");

                    b.ToTable("MedicalRecords", (string)null);
                });

            modelBuilder.Entity("Midterm1212.Models.Patient", b =>
                {
                    b.Property<int>("patient_id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("patient_id"));

                    b.Property<string>("address")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime>("dob")
                        .HasColumnType("datetime2");

                    b.Property<string>("gender")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("phone")
                        .IsRequired()
                        .HasMaxLength(15)
                        .HasColumnType("nvarchar(15)");

                    b.HasKey("patient_id");

                    b.ToTable("Patients", (string)null);
                });

            modelBuilder.Entity("Midterm1212.Models.Appointment", b =>
                {
                    b.HasOne("Midterm1212.Models.Doctor", "Doctor")
                        .WithMany("Appointments")
                        .HasForeignKey("doctor_id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Midterm1212.Models.Patient", "Patient")
                        .WithMany("Appointments")
                        .HasForeignKey("patient_id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Doctor");

                    b.Navigation("Patient");
                });

            modelBuilder.Entity("Midterm1212.Models.MedicalRecord", b =>
                {
                    b.HasOne("Midterm1212.Models.Appointment", "Appointment")
                        .WithMany("MedicalRecords")
                        .HasForeignKey("appointment_id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Midterm1212.Models.Doctor", "Doctor")
                        .WithMany("MedicalRecords")
                        .HasForeignKey("doctor_id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Midterm1212.Models.Patient", "Patient")
                        .WithMany("MedicalRecords")
                        .HasForeignKey("patient_id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Appointment");

                    b.Navigation("Doctor");

                    b.Navigation("Patient");
                });

            modelBuilder.Entity("Midterm1212.Models.Appointment", b =>
                {
                    b.Navigation("MedicalRecords");
                });

            modelBuilder.Entity("Midterm1212.Models.Doctor", b =>
                {
                    b.Navigation("Appointments");

                    b.Navigation("MedicalRecords");
                });

            modelBuilder.Entity("Midterm1212.Models.Patient", b =>
                {
                    b.Navigation("Appointments");

                    b.Navigation("MedicalRecords");
                });
#pragma warning restore 612, 618
        }
    }
}
